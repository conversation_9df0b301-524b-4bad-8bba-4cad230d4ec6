#!/usr/bin/env python3
"""Test script for PyGAD convergence criteria conversion."""

import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "optimagic_", "src"))

import numpy as np
from optimagic.optimizers.pygad_new import (
    Pygad,
    ReachFitnessCriterion,
    SaturateFitnessCriterion,
    CombinedConvergenceCriteria,
    _convert_convergence_to_pygad_stop_criteria,
)


def test_reach_fitness_criterion():
    """Test ReachFitnessCriterion conversion."""
    criterion = ReachFitnessCriterion(target_fitness=100.0)
    result = criterion.to_pygad_stop_criteria()
    expected = ["reach_100.0"]
    assert result == expected, f"Expected {expected}, got {result}"
    print("✓ ReachFitnessCriterion test passed")


def test_saturate_fitness_criterion():
    """Test SaturateFitnessCriterion conversion."""
    criterion = SaturateFitnessCriterion(generations=10)
    result = criterion.to_pygad_stop_criteria()
    expected = ["saturate_10"]
    assert result == expected, f"Expected {expected}, got {result}"
    print("✓ SaturateFitnessCriterion test passed")


def test_combined_convergence_criteria():
    """Test CombinedConvergenceCriteria conversion."""
    criteria = [
        ReachFitnessCriterion(target_fitness=50.0),
        SaturateFitnessCriterion(generations=5),
    ]
    combined = CombinedConvergenceCriteria(criteria)
    result = combined.to_pygad_stop_criteria()
    expected = ["reach_50.0", "saturate_5"]
    assert result == expected, f"Expected {expected}, got {result}"
    print("✓ CombinedConvergenceCriteria test passed")


def test_conversion_function():
    """Test the main conversion function."""
    # Test with individual parameters
    result = _convert_convergence_to_pygad_stop_criteria(
        convergence_ftol_abs=0.0,
        convergence_max_generations_without_improvement=15,
        convergence_target_fitness=75.0,
        convergence_criteria=None,
    )
    expected = ["reach_75.0", "saturate_15"]
    assert result == expected, f"Expected {expected}, got {result}"
    print("✓ Conversion function with individual parameters test passed")

    # Test with convergence_criteria instance
    criterion = ReachFitnessCriterion(target_fitness=200.0)
    result = _convert_convergence_to_pygad_stop_criteria(
        convergence_ftol_abs=0.0,
        convergence_max_generations_without_improvement=None,
        convergence_target_fitness=None,
        convergence_criteria=criterion,
    )
    expected = ["reach_200.0"]
    assert result == expected, f"Expected {expected}, got {result}"
    print("✓ Conversion function with criterion instance test passed")

    # Test with list of criteria
    criteria = [
        ReachFitnessCriterion(target_fitness=100.0),
        SaturateFitnessCriterion(generations=20),
    ]
    result = _convert_convergence_to_pygad_stop_criteria(
        convergence_ftol_abs=0.0,
        convergence_max_generations_without_improvement=None,
        convergence_target_fitness=None,
        convergence_criteria=criteria,
    )
    expected = ["reach_100.0", "saturate_20"]
    assert result == expected, f"Expected {expected}, got {result}"
    print("✓ Conversion function with criteria list test passed")


def test_runtime_checkable():
    """Test that dataclasses are runtime checkable."""
    from optimagic.optimizers.pygad_new import BaseConvergenceCriterion
    
    criterion = ReachFitnessCriterion(target_fitness=50.0)
    assert isinstance(criterion, BaseConvergenceCriterion), "ReachFitnessCriterion should be instance of BaseConvergenceCriterion"
    
    criterion = SaturateFitnessCriterion(generations=10)
    assert isinstance(criterion, BaseConvergenceCriterion), "SaturateFitnessCriterion should be instance of BaseConvergenceCriterion"
    
    print("✓ Runtime checkable test passed")


def test_pygad_class_parameters():
    """Test that Pygad class accepts the new convergence parameters."""
    # Test with individual convergence parameters
    optimizer = Pygad(
        convergence_target_fitness=100.0,
        convergence_max_generations_without_improvement=10,
        convergence_ftol_abs=1e-6,
    )
    assert optimizer.convergence_target_fitness == 100.0
    assert optimizer.convergence_max_generations_without_improvement == 10
    assert optimizer.convergence_ftol_abs == 1e-6
    print("✓ Pygad class individual parameters test passed")

    # Test with convergence_criteria
    criterion = ReachFitnessCriterion(target_fitness=50.0)
    optimizer = Pygad(convergence_criteria=criterion)
    assert optimizer.convergence_criteria == criterion
    print("✓ Pygad class convergence_criteria test passed")


if __name__ == "__main__":
    print("Testing PyGAD convergence criteria conversion...")
    
    test_reach_fitness_criterion()
    test_saturate_fitness_criterion()
    test_combined_convergence_criteria()
    test_conversion_function()
    test_runtime_checkable()
    test_pygad_class_parameters()
    
    print("\n🎉 All tests passed!")
